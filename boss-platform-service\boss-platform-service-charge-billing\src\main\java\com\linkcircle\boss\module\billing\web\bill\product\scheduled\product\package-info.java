/**
 * <AUTHOR>
 * @date 2025-07-08 11:45
 * @description 后付费 按产品出账
 * 0. 查询所有订阅的账户ID列表
 * 1. 根据账户ID获取订阅列表, 按产品分组, 查询产品下的全部服务
 * 2. 查询服务账单表 是否全部出账完成 postpaid_product_service_income_bill, 判断是否全部出账完成
 *  COMMENT ON COLUMN "public"."postpaid_product_service_income_bill"."billing_status" IS '出账状态 0-待处理, 1-处理中, 2-已完成, 3-失败';
 * 3. 有服务没有完成出账的, 手动调用服务出账, 
 * 4. 服务全部完成出账, 在内存里统计消耗量合计
 * 5. 生成产品账单id 更新服务账单表 产品账单id bill_id 字段
 * 6. 新增服务账单表记录
 */
package com.linkcircle.boss.module.billing.web.bill.product.scheduled.product;