package com.linkcircle.boss.module.billing.web.bill.product.scheduled.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.product.service.IncomePostpaidProductBillingService;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025-07-08 11:45
 * @description 后付费产品出账定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IncomePostpaidProductBillingScheduledTask {

    private final IncomePostpaidProductBillingService productBillingService;
    private final SubscriptionDataService subscriptionDataService;
    private final RedissonUtil redissonUtil;

    private static final String LOCK_KEY_PREFIX = "billing:income:postpaid:product";
    private static final Duration LOCK_TIMEOUT = Duration.ofMinutes(30);

    @XxlJob("incomePostpaidProductBillingHandler")
    @XxlJobRegister(
            cron = "0 45 * * * ?",
            jobDesc = "收入-后付费-产品出账定时任务",
            author = "admin",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.LEAST_FREQUENTLY_USED,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void incomePostpaidProductBillingHandler() {
        String traceId = IdUtil.fastSimpleUUID();
        TraceIdUtil.buildAndSetTraceId(" ", "收入-后付费产品出账", traceId);

        try {
            EnableLoginContext.setContext(false);
            log.info("开始执行后付费产品出账定时任务");

            String lockKey = ChargeCacheUtils.getProcessLockKey(LOCK_KEY_PREFIX, String.valueOf(PaymentTypeEnum.POSTPAID));
            RLock lock = redissonUtil.getLock(lockKey);
            boolean isLocked = false;

            try {
                isLocked = lock.tryLock(30, LOCK_TIMEOUT.toSeconds(), TimeUnit.SECONDS);
                if (!isLocked) {
                    log.info("产品出账任务正在执行中，跳过本次执行, lockKey: {}", lockKey);
                    return;
                }

                executeProductBilling();
                log.info("后付费产品出账定时任务执行完成");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("获取分布式锁被中断, lockKey: {}", lockKey, e);
            } catch (Exception e) {
                log.error("执行后付费产品出账定时任务异常", e);
            } finally {
                if (isLocked && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } finally {
            EnableLoginContext.clearContext();
            TraceIdUtil.remove();
        }
    }

    public void executeProductBilling() {
        List<Long> accountIds = subscriptionDataService.getAllSubscriptionAccountIds(PaymentTypeEnum.POSTPAID.getMethod());
        if (CollUtil.isEmpty(accountIds)) {
            log.info("未找到需要产品出账的账户");
            return;
        }

        log.info("找到需要产品出账的账户数量: {}", accountIds.size());

        int processedCount = 0;
        int successCount = 0;
        int failureCount = 0;

        for (Long accountId : accountIds) {
            processedCount++;
            try {
                log.info("开始处理账户产品出账, accountId: {}, 进度: {}/{}", accountId, processedCount, accountIds.size());
                productBillingService.processAccountProductBilling(accountId);
                successCount++;
            } catch (Exception e) {
                failureCount++;
                log.error("账户产品出账处理失败, accountId: {}", accountId, e);
            }

            if (processedCount % 10 == 0) {
                log.info("产品出账进度统计, 已处理: {}/{}, 成功: {}, 失败: {}",
                        processedCount, accountIds.size(), successCount, failureCount);
            }
        }

        log.info("所有账户产品出账处理完成, 总数: {}, 成功: {}, 失败: {}", processedCount, successCount, failureCount);
    }
}